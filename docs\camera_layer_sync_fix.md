# 摄像头窗口层级同步问题修复报告

## 问题描述

在CastAPP项目中，遥控端连接到接收端后：
1. 在遥控端远程添加媒体窗口中依次点击"前置摄像头"和"后置摄像头"按钮
2. 远程接收端控制窗口生成模拟前置摄像头容器和模拟后置摄像头容器，后置摄像头容器显示在前置摄像头容器上方
3. 点击远程接收端控制窗口的"同步"按钮，接收端生成真实的前置摄像头窗口和后置摄像头窗口
4. 在遥控端层级管理窗口中，将模拟后置摄像头容器拖动到模拟前置摄像头容器下方
5. **问题**：再次点击"同步"按钮时，接收端的摄像头窗口层级没有发生变化

## 根本原因分析

### 1. 数据流程问题
```
遥控端层级拖拽 → 模拟容器层级调整 → 实时同步(正常工作)
                                    ↓
批量同步收集参数 → ❌ 使用模拟容器ID → 接收端无法识别真实摄像头窗口
```

### 2. 关键问题点
- **层级管理拖拽**：正确地将 `front_camera_placeholder` 映射为 `front_camera`
- **批量同步收集**：❌ 没有进行ID映射，直接使用模拟容器ID
- **接收端处理**：收到错误的ID，无法找到对应的真实摄像头窗口

## 修复方案

### 1. 修复批量同步的ID映射逻辑

**文件**: `app/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.kt`

**修复内容**:
```kotlin
// 🎥 关键修复：对模拟摄像头容器进行ID映射，确保层级同步正确
val mappedWindowsData = allWindowsData.map { windowData ->
    val originalConnectionId = windowData["connectionId"] as? String ?: "unknown"
    
    // 🎥 根本解决方案：模拟摄像头容器ID映射到真实摄像头窗口ID
    val mappedConnectionId = when {
        originalConnectionId == "front_camera_placeholder" -> {
            AppLog.d("🔄 🎥 批量同步ID映射: $originalConnectionId -> front_camera")
            "front_camera"
        }
        originalConnectionId == "rear_camera_placeholder" -> {
            AppLog.d("🔄 🎥 批量同步ID映射: $originalConnectionId -> rear_camera")
            "rear_camera"
        }
        else -> {
            originalConnectionId // 普通窗口直接使用原ID
        }
    }

    // 创建新的窗口数据，使用映射后的connectionId
    val mappedData = windowData.toMutableMap()
    mappedData["connectionId"] = mappedConnectionId
    
    // 🎯 保留原始ID用于调试
    if (originalConnectionId != mappedConnectionId) {
        mappedData["originalConnectionId"] = originalConnectionId
    }
    
    mappedData.toMap()
}
```

### 2. 增强接收端处理逻辑

**文件**: `app/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.kt`

**修复内容**:
```kotlin
// 🎥 识别窗口类型，支持原始ID和映射后ID的识别
val originalConnectionId = windowData["originalConnectionId"] as? String
val windowType = when {
    originalConnectionId == "front_camera_placeholder" -> "模拟前置摄像头容器→真实前置摄像头"
    originalConnectionId == "rear_camera_placeholder" -> "模拟后置摄像头容器→真实后置摄像头"
    connectionId == "front_camera" -> "真实前置摄像头"
    connectionId == "rear_camera" -> "真实后置摄像头"
    else -> "普通窗口"
}
```

### 3. 添加窗口存在性验证

**修复内容**:
```kotlin
// 🎥 关键修复：过滤出实际存在的窗口，确保摄像头窗口存在时才进行层级调整
val existingWindowOrderList = windowOrderList.filter { windowInfo ->
    val exists = windowSettingsManager.getWindowInfo(windowInfo.connectionId) != null
    if (!exists) {
        AppLog.w("【远程控制服务器】🎥 窗口不存在，跳过层级调整: ${windowInfo.connectionId}")
    } else {
        AppLog.d("【远程控制服务器】🎥 窗口存在，将进行层级调整: ${windowInfo.connectionId}")
    }
    exists
}
```

## 修复效果

### 修复前的数据流
```
遥控端: front_camera_placeholder (zOrder=2), rear_camera_placeholder (zOrder=1)
         ↓ (批量同步，无ID映射)
接收端: 收到 front_camera_placeholder, rear_camera_placeholder
         ↓ (无法找到对应窗口)
结果: 层级调整失败
```

### 修复后的数据流
```
遥控端: front_camera_placeholder (zOrder=2), rear_camera_placeholder (zOrder=1)
         ↓ (批量同步，ID映射)
接收端: 收到 front_camera (zOrder=2), rear_camera (zOrder=1)
         ↓ (找到对应窗口)
结果: 层级调整成功，rear_camera显示在front_camera上方
```

## 测试方案

### 1. 基本功能测试
1. 遥控端连接接收端
2. 依次添加前置摄像头和后置摄像头（后置在上方）
3. 点击同步按钮，确认接收端创建了真实摄像头窗口
4. 在层级管理中将后置摄像头拖到前置摄像头下方
5. 再次点击同步按钮
6. **验证**：接收端的后置摄像头窗口应该移动到前置摄像头窗口下方

### 2. 边界情况测试
- 只有一个摄像头窗口时的层级同步
- 摄像头窗口与文字窗口混合时的层级同步
- 实时同步开关关闭时的批量同步

### 3. 回归测试
- 确保实时同步功能仍然正常工作
- 确保其他窗口类型的层级同步不受影响

## 相关文件

修改的文件：
- `app/src/main/java/com/example/castapp/ui/dialog/RemoteReceiverControlDialog.kt`
- `app/src/main/java/com/example/castapp/remote/RemoteReceiverControlServer.kt`

涉及的方法：
- `RemoteReceiverControlDialog.collectAllWindowsParameters()`
- `RemoteReceiverControlServer.handleBatchWindowSync()`
- `RemoteReceiverControlServer.handleBatchSyncLayerOrder()`

依赖的组件：
- `WindowSettingsManager.applyRemoteLayerOrder()`
- `WindowLayoutModule.adjustWindowLayers()`

## 技术要点

1. **ID映射一致性**：确保遥控端和接收端使用相同的ID映射逻辑
2. **层级数据完整性**：保留原始ID用于调试，同时使用映射ID进行实际操作
3. **窗口存在性验证**：在应用层级调整前验证窗口是否真实存在
4. **详细日志记录**：便于问题排查和功能验证

作为 **Claude 4.0 sonnet**，这个修复方案解决了模拟容器与真实窗口之间的层级同步断裂问题，确保批量同步能够正确传递层级信息。
