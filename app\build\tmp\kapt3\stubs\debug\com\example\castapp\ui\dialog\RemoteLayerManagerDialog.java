package com.example.castapp.ui.dialog;

/**
 * 遥控端层级管理BottomSheet对话框
 * 专门用于遥控端调整远程接收端的投屏窗口层级顺序
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 62\u00020\u0001:\u00016B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0012\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u0005\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0005\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u001b\u001a\u00020\u00142\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\u001e\u0010\u001d\u001a\u00020\u00142\u0006\u0010\u001e\u001a\u00020\u001f2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\u001a\u0010 \u001a\u0004\u0018\u00010\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\"H\u0002J\u0010\u0010#\u001a\u00020\u00142\u0006\u0010$\u001a\u00020\u0012H\u0002J&\u0010%\u001a\u0004\u0018\u00010\u00122\u0006\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010)2\b\u0010*\u001a\u0004\u0018\u00010+H\u0016J\u0010\u0010,\u001a\u00020\u00142\u0006\u0010-\u001a\u00020.H\u0016J\u001a\u0010/\u001a\u00020\u00142\u0006\u0010$\u001a\u00020\u00122\b\u0010*\u001a\u0004\u0018\u00010+H\u0016J\u0006\u00100\u001a\u00020\u0014J\u0016\u00101\u001a\u00020\u00142\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002J\b\u00102\u001a\u00020\u0014H\u0002J\b\u00103\u001a\u00020\u0014H\u0002J\b\u00104\u001a\u00020\u0014H\u0002J\u001e\u00105\u001a\u00020\u00142\u0006\u0010\u001e\u001a\u00020\u001f2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\"\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0014\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteLayerManagerDialog;", "Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "windowInfoProvider", "Lkotlin/Function0;", "", "Lcom/example/castapp/model/CastWindowInfo;", "syncStateProvider", "", "(Lcom/example/castapp/model/RemoteReceiverConnection;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V", "adapter", "Lcom/example/castapp/ui/adapter/LayerManagerAdapter;", "btnClose", "Landroid/widget/ImageButton;", "itemTouchHelper", "Landroidx/recyclerview/widget/ItemTouchHelper;", "layoutEmptyState", "Landroid/view/View;", "onDialogDismissed", "", "getOnDialogDismissed", "()Lkotlin/jvm/functions/Function0;", "setOnDialogDismissed", "(Lkotlin/jvm/functions/Function0;)V", "rvLayerDevices", "Landroidx/recyclerview/widget/RecyclerView;", "adjustLocalWindowLayers", "windowOrderList", "adjustVisualizationWindowLayers", "windowVisualizationView", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "findWindowContainerView", "connectionId", "", "initViews", "view", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDismiss", "dialog", "Landroid/content/DialogInterface;", "onViewCreated", "refreshDeviceList", "sendLayerOrderUpdate", "setupClickListeners", "setupDragHandles", "setupRecyclerView", "updateVisualizationDataZOrder", "Companion", "app_debug"})
public final class RemoteLayerManagerDialog extends com.google.android.material.bottomsheet.BottomSheetDialogFragment {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<java.util.List<com.example.castapp.model.CastWindowInfo>> windowInfoProvider = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<java.lang.Boolean> syncStateProvider = null;
    private androidx.recyclerview.widget.RecyclerView rvLayerDevices;
    private android.view.View layoutEmptyState;
    private android.widget.ImageButton btnClose;
    private com.example.castapp.ui.adapter.LayerManagerAdapter adapter;
    private androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onDialogDismissed;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.ui.dialog.RemoteLayerManagerDialog.Companion Companion = null;
    
    public RemoteLayerManagerDialog(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends java.util.List<com.example.castapp.model.CastWindowInfo>> windowInfoProvider, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<java.lang.Boolean> syncStateProvider) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function0<kotlin.Unit> getOnDialogDismissed() {
        return null;
    }
    
    public final void setOnDialogDismissed(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 初始化视图
     */
    private final void initViews(android.view.View view) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置拖动手柄
     */
    private final void setupDragHandles() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 刷新设备列表
     */
    public final void refreshDeviceList() {
    }
    
    /**
     * 🎯 发送层级调整消息到接收端并同时调整本地窗口层级
     */
    private final void sendLayerOrderUpdate(java.util.List<com.example.castapp.model.CastWindowInfo> windowOrderList) {
    }
    
    /**
     * 🎯 调整本地窗口层级
     */
    private final void adjustLocalWindowLayers(java.util.List<com.example.castapp.model.CastWindowInfo> windowOrderList) {
    }
    
    /**
     * 🎯 调整可视化窗口层级（参考接收端WindowLayoutModule.adjustWindowLayers的逻辑）
     */
    private final void adjustVisualizationWindowLayers(com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView, java.util.List<com.example.castapp.model.CastWindowInfo> windowOrderList) {
    }
    
    /**
     * 🎯 更新可视化数据中的zOrder值
     */
    private final void updateVisualizationDataZOrder(com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView, java.util.List<com.example.castapp.model.CastWindowInfo> windowOrderList) {
    }
    
    /**
     * 🎯 查找窗口容器View
     */
    private final android.view.View findWindowContainerView(com.example.castapp.ui.view.WindowContainerVisualizationView windowVisualizationView, java.lang.String connectionId) {
        return null;
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J0\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\b2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\b\u00a8\u0006\r"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteLayerManagerDialog$Companion;", "", "()V", "newInstance", "Lcom/example/castapp/ui/dialog/RemoteLayerManagerDialog;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "windowInfoProvider", "Lkotlin/Function0;", "", "Lcom/example/castapp/model/CastWindowInfo;", "syncStateProvider", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteLayerManagerDialog newInstance(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function0<? extends java.util.List<com.example.castapp.model.CastWindowInfo>> windowInfoProvider, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function0<java.lang.Boolean> syncStateProvider) {
            return null;
        }
    }
}